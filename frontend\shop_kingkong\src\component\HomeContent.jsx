import React from "react";

const HomePage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-b from-pink-50 to-purple-50">
      {/* Hero Section */}
      <div className="container mx-auto px-8 py-16">
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-gray-800 mb-6">
            <span className="bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent">
              TÚI XA XỈ CHO PHÁI ĐẸP
            </span>
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Khám phá bộ sưu tập túi xách cao cấp dành riêng cho những người phụ
            nữ hiện đại và tinh tế
          </p>
        </div>

        {/* Featured Categories */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          <div className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
            <div className="h-48 bg-gradient-to-br from-pink-200 to-pink-300"></div>
            <div className="p-6">
              <h3 className="text-xl font-semibold text-gray-800 mb-2">
                Túi Xách Tay
              </h3>
              <p className="text-gray-600">
                Những chiếc túi xách tay thanh lịch, phù hợp cho mọi dịp
              </p>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
            <div className="h-48 bg-gradient-to-br from-purple-200 to-purple-300"></div>
            <div className="p-6">
              <h3 className="text-xl font-semibold text-gray-800 mb-2">
                Túi Đeo Chéo
              </h3>
              <p className="text-gray-600">
                Tiện lợi và thời trang cho cuộc sống năng động
              </p>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
            <div className="h-48 bg-gradient-to-br from-indigo-200 to-indigo-300"></div>
            <div className="p-6">
              <h3 className="text-xl font-semibold text-gray-800 mb-2">
                Túi Clutch
              </h3>
              <p className="text-gray-600">
                Sang trọng và quyến rũ cho những buổi tiệc tối
              </p>
            </div>
          </div>
        </div>

        {/* Special Offers */}
        <div className="bg-white rounded-xl shadow-lg p-8 mb-16">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-800 mb-4">
              Ưu Đãi Đặc Biệt
            </h2>
            <p className="text-lg text-gray-600 mb-6">
              Giảm giá lên đến 50% cho bộ sưu tập mùa hè
            </p>
            <button className="bg-gradient-to-r from-pink-500 to-purple-600 text-white px-8 py-3 rounded-full font-semibold hover:from-pink-600 hover:to-purple-700 transition-all duration-300 transform hover:scale-105">
              Khám Phá Ngay
            </button>
          </div>
        </div>

        {/* Why Choose Us */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-20">
          <div className="text-center p-6">
            <div className="w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">🎁</span>
            </div>
            <h3 className="font-semibold text-gray-800 mb-2">Chất Lượng Cao</h3>
            <p className="text-gray-600 text-sm">
              Sản phẩm được tuyển chọn kỹ lưỡng
            </p>
          </div>

          <div className="text-center p-6">
            <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">🚚</span>
            </div>
            <h3 className="font-semibold text-gray-800 mb-2">
              Giao Hàng Nhanh
            </h3>
            <p className="text-gray-600 text-sm">Miễn phí ship toàn quốc</p>
          </div>

          <div className="text-center p-6">
            <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">💎</span>
            </div>
            <h3 className="font-semibold text-gray-800 mb-2">
              Thiết Kế Độc Quyền
            </h3>
            <p className="text-gray-600 text-sm">
              Những mẫu túi độc đáo và sang trọng
            </p>
          </div>

          <div className="text-center p-6">
            <div className="w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">🔄</span>
            </div>
            <h3 className="font-semibold text-gray-800 mb-2">
              Đổi Trả Dễ Dàng
            </h3>
            <p className="text-gray-600 text-sm">
              Chính sách đổi trả trong 30 ngày
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
